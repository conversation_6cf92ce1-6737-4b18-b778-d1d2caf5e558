<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref, onMounted, onUnmounted } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { $t } from '@vben/locales';

import { Button, message, Modal, Image } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteNews,
  getNewsList,
  publishNews,
  batchOperateNews,
  getAllCategories,
  type NewsManagementApi,
} from '#/api/news-management';

import { useColumns, useGridFormSchema, NEWS_STATUS_CONFIG } from './data';
import Form from './modules/form.vue';

defineOptions({
  name: 'NewsManagementList',
});

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

// 组件卸载状态
const isUnmounted = ref(false);

// 分类选项
const categoryOptions = ref<Array<{ label: string; value: number }>>([]);

// 加载分类选项
async function loadCategories() {
  try {
    const res = await getAllCategories();
    if (res && res.data && Array.isArray(res.data)) {
      categoryOptions.value = res.data.map(cat => ({
        label: cat.name,
        value: cat.id,
      }));
    } else {
      categoryOptions.value = [];
    }
  } catch (error) {
    console.error('加载分类失败:', error);
    categoryOptions.value = [];
  }
}

function confirm(content: string, title?: string) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: title || '确认',
      content,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        resolve(true);
      },
      onCancel: () => {
        reject(new Error('用户取消操作'));
      },
    });
  });
}

// 操作处理
function onActionClick(e: OnActionClickParams<NewsManagementApi.News>) {
  switch (e.action) {
    case 'view':
      onView(e.row);
      break;
    case 'edit':
      onEdit(e.row);
      break;
    case 'delete':
      onDelete(e.row);
      break;
    case 'publish':
      onPublish(e.row);
      break;
    case 'unpublish':
      onUnpublish(e.row);
      break;
    case 'preview':
      onPreview(e.row);
      break;
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    showOverflow: true,
    columnConfig: {
      resizable: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          if (isUnmounted.value) {
            return { result: [], total: 0 };
          }

          try {
            const params = { ...formValues };

            // 处理日期范围
            if (params.dateRange && Array.isArray(params.dateRange)) {
              params.startDate = params.dateRange[0];
              params.endDate = params.dateRange[1];
              delete params.dateRange;
            }

            const res = await getNewsList({
              page: page.currentPage,
              pageSize: page.pageSize,
              ...params,
            });

            if (isUnmounted.value) {
              return { result: [], total: 0 };
            }

            return {
              result: res.list || [],
              total: res.pagination?.total || 0,
            };
          } catch (error) {
            if (!isUnmounted.value) {
              console.error('获取新闻列表失败:', error);
            }
            return { result: [], total: 0 };
          }
        },
      },
      response: {
        result: 'result',
        total: 'total',
      },
    },
    pagerConfig: {},
    rowConfig: {
      keyField: 'id',
      height: 110, // 设置行高以适应封面图片
    },
    checkboxConfig: {
      reserve: true,
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: true,
      refreshOptions: { code: 'query' },
      search: true,
      zoom: true,
      zoomOptions: {},
    },
  } as VxeTableGridOptions,
});

// 新增新闻
function onCreate() {
  formDrawerApi.open();
}

// 查看新闻
function onView(row: NewsManagementApi.News) {
  // 这里可以实现查看功能，比如跳转到详情页或打开详情弹窗
  console.log('查看新闻:', row);
  message.info('查看功能待实现');
}

// 编辑新闻
function onEdit(row: NewsManagementApi.News) {
  console.log('点击编辑，原始数据:', row);
  const editData = {
    ...row,
    category_id: row.category?.id,
    tags: row.tags?.map(tag => tag.name) || [],
  };
  console.log('传递给表单的编辑数据:', editData);
  formDrawerApi.setData(editData).open();
}

// 删除新闻
async function onDelete(row: NewsManagementApi.News) {
  if (isUnmounted.value) return;

  try {
    await confirm(`确定要删除新闻 "${row.title}" 吗？`, '删除确认');
    if (isUnmounted.value) return;

    await deleteNews(row.id);
    if (isUnmounted.value) return;

    message.success('删除成功');
    gridApi.grid?.commitProxy('query');
  } catch (error: any) {
    if (error.message !== '用户取消操作' && !isUnmounted.value) {
      message.error(error.message || '删除失败');
    }
  }
}

// 发布新闻
async function onPublish(row: NewsManagementApi.News) {
  if (isUnmounted.value) return;

  try {
    await confirm(`确定要发布新闻 "${row.title}" 吗？`, '发布确认');
    if (isUnmounted.value) return;

    await publishNews(row.id, { action: 'publish' });
    if (isUnmounted.value) return;

    message.success('发布成功');
    gridApi.grid?.commitProxy('query');
  } catch (error: any) {
    if (error.message !== '用户取消操作' && !isUnmounted.value) {
      message.error(error.message || '发布失败');
    }
  }
}

// 下线新闻
async function onUnpublish(row: NewsManagementApi.News) {
  if (isUnmounted.value) return;

  try {
    await confirm(`确定要下线新闻 "${row.title}" 吗？`, '下线确认');
    if (isUnmounted.value) return;

    await publishNews(row.id, { action: 'unpublish' });
    if (isUnmounted.value) return;

    message.success('下线成功');
    gridApi.grid?.commitProxy('query');
  } catch (error: any) {
    if (error.message !== '用户取消操作' && !isUnmounted.value) {
      message.error(error.message || '下线失败');
    }
  }
}

// 预览新闻
function onPreview(row: NewsManagementApi.News) {
  // 打开新窗口预览新闻
  const previewUrl = `/news/${row.id}`;
  window.open(previewUrl, '_blank');
}

// 批量操作
async function onBatchOperation(action: string) {
  const selectedRows = gridApi.getCheckboxRecords();
  
  if (selectedRows.length === 0) {
    message.warning('请先选择要操作的新闻');
    return;
  }

  const actionMap = {
    publish: '发布',
    unpublish: '下线',
    delete: '删除',
    archive: '归档',
  };

  try {
    await confirm(
      `确定要${actionMap[action]}选中的 ${selectedRows.length} 条新闻吗？`,
      '批量操作确认'
    );

    const ids = selectedRows.map(row => row.id);
    await batchOperateNews({ ids, action });
    
    gridApi.reload();
    message.success(`批量${actionMap[action]}成功`);
  } catch (error: any) {
    if (error.message !== '用户取消操作') {
      message.error(error.message || '操作失败');
    }
  }
}

// 状态处理函数
function getStatusText(status: string) {
  const statusMap = {
    draft: '草稿',
    pending: '待审核',
    published: '已发布',
    archived: '已归档'
  };
  return statusMap[status] || status;
}

function getStatusClass(status: string) {
  const classMap = {
    draft: 'text-gray-500',
    pending: 'text-orange-500',
    published: 'text-green-500',
    archived: 'text-red-500'
  };
  return classMap[status] || 'text-gray-500';
}

// 切换状态
async function onToggleStatus(row: any) {
  try {
    const newStatus = row.status === 'published' ? 'draft' : 'published';
    const statusText = newStatus === 'published' ? '发布' : '下线';

    await confirm(`确定要${statusText}新闻 "${row.title}" 吗？`, '状态切换');

    // 发送API请求
    await publishNews(row.id, { status: newStatus });

    message.success(`新闻${statusText}成功`);

    // 刷新表格数据
    gridApi.grid?.commitProxy('query');
  } catch (error) {
    // 用户取消操作或操作失败
  }
}

// 生命周期钩子
onMounted(() => {
  loadCategories();
});

onUnmounted(() => {
  isUnmounted.value = true;
});
</script>

<template>
  <Page auto-content-height>
    <FormDrawer :on-success="() => gridApi.grid?.commitProxy('query')" />
    <Grid :table-title="'新闻管理'">
      <template #toolbar-tools>
        <div class="flex gap-2">
          <Button type="primary" @click="onCreate">
            <Plus class="size-5" />
            {{ $t('ui.actionTitle.create', ['新闻']) }}
          </Button>
          <Button @click="onBatchOperation('publish')">
            批量发布
          </Button>
          <Button @click="onBatchOperation('unpublish')">
            批量下线
          </Button>
          <Button danger @click="onBatchOperation('delete')">
            批量删除
          </Button>
        </div>
      </template>

      <!-- 自定义封面图片显示 -->
      <template #coverImage="{ row }">
        <Image
          v-if="row.coverImage"
          :src="row.coverImage"
          :alt="row.title"
          :width="80"
          :height="45"
          :style="{ objectFit: 'cover', borderRadius: '4px' }"
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
        <span v-else class="text-gray-400">无图片</span>
      </template>

      <!-- 自定义状态显示 -->
      <template #status="{ row }">
        <span :class="getStatusClass(row.status)">
          {{ getStatusText(row.status) }}
        </span>
      </template>

      <!-- 自定义操作按钮 -->
      <template #operation="{ row }">
        <div class="flex gap-2">
          <Button type="link" size="small" @click="onView(row)">
            查看
          </Button>
          <Button type="link" size="small" @click="onEdit(row)">
            编辑
          </Button>
          <Button type="link" size="small" danger @click="onDelete(row)">
            删除
          </Button>
          <!-- 根据状态显示不同的按钮 -->
          <Button
            v-if="row.status === 'published'"
            type="link"
            size="small"
            danger
            @click="onToggleStatus(row)"
          >
            下线
          </Button>
          <Button
            v-else
            type="link"
            size="small"
            @click="onToggleStatus(row)"
          >
            发布
          </Button>
        </div>
      </template>
    </Grid>
  </Page>
</template>
